# @package memory
# MemFuse Memory System Configuration
# Optimized configuration for M0/M1/M2 memory hierarchy
#
# Memory Layer Architecture (Updated Naming):
# - M0: Raw Data Layer - Stores original data in unprocessed form
# - M1: Episodic Memory Layer - Stores event-centered experiences and contexts
# - M2: Semantic Memory Layer - Extracts and stores facts and concepts
# - M3: Procedural Memory Layer - Planned for future implementation

# =============================================================================
# CORE ARCHITECTURE SETTINGS
# =============================================================================

# Processing strategy - controls how layers work together
processing:
  strategy: "parallel"          # parallel | sequential
  enable_fallback: true         # fallback to sequential if parallel fails
  max_concurrent_layers: 3      # maximum layers processing simultaneously

  # Global timeout and retry settings
  timeout:
    per_layer: 30.0            # timeout per layer operation (seconds)
    total_operation: 120.0     # total operation timeout (seconds)

  retry:
    max_attempts: 3            # maximum retry attempts
    base_delay: 1.0           # base delay between retries (seconds)
    max_delay: 10.0           # maximum delay between retries (seconds)
    exponential_backoff: true # use exponential backoff

# =============================================================================
# MEMORY LAYERS CONFIGURATION
# =============================================================================

layers:
  # M0: Raw Data - Original data storage
  m0:
    enabled: true
    priority: 1

    # M0-specific settings
    buffer_size: 1000
    auto_flush_interval: 60    # seconds
    storage_backends: ["vector", "keyword", "sql"]

    # M0 storage tables
    storage:
      message_table: "m0_raw"
      metadata_table: "m0_metadata"

  # M1: Episodic Memory - Event-centered experiences
  m1:
    enabled: true
    priority: 2

    # M1-specific settings
    episode_formation_enabled: true
    llm_model: "grok-3-mini"
    batch_size: 5
    min_confidence_threshold: 0.7

    # M1 storage tables
    storage:
      episode_table: "m1_episodic"
      lineage_table: "m1_lineage"
      metadata_table: "m1_metadata"

  # M2: Semantic Memory - Facts and concepts
  m2:
    enabled: true
    priority: 3

    # M2-specific settings
    fact_extraction_enabled: true
    llm_model: "grok-3-mini"
    batch_size: 5
    min_confidence_threshold: 0.7

    # M2 storage tables
    storage:
      fact_table: "m2_semantic"
      lineage_table: "m2_lineage"
      conflicts_table: "m2_conflicts"

  # M3: Procedural Memory - Learned patterns and procedures
  m3:
    enabled: true  # Now fully implemented
    priority: 4

    # M3-specific settings
    pattern_recognition_enabled: true
    skill_learning_enabled: true
    behavior_adaptation_enabled: true
    llm_model: "grok-3-mini"
    batch_size: 3
    min_confidence_threshold: 0.8

    # M3 storage tables
    storage:
      pattern_table: "m3_procedural"
      skill_table: "m3_skills"
      behavior_table: "m3_behaviors"

  # MSMG: Multi-Scale Mental Graph - Meta-structure for knowledge organization
  msmg:
    enabled: true
    priority: 5

    # MSMG-specific settings
    graph_construction_enabled: true
    ontology_mapping_enabled: true
    metacognitive_tracking_enabled: true
    forgetting_mechanism_enabled: true
    llm_model: "grok-3-mini"
    batch_size: 5
    min_confidence_threshold: 0.7

    # MSMG storage tables
    storage:
      instances_table: "msmg_instances"
      ontology_table: "msmg_ontology"
      relations_table: "msmg_relations"
      metacognitive_table: "msmg_metacognitive"

# =============================================================================
# STORAGE BACKEND CONFIGURATION
# =============================================================================

storage:
  # Backend-specific connection settings (inherits from store.pgai config)
  vector:
    connection_pool_size: 5
    timeout: 30.0
    batch_size: 100
    # backend inherited from store config

  keyword:
    connection_pool_size: 3
    timeout: 15.0
    enable_transactions: true
    backend: "sqlite"  # Keyword search optimized for SQLite

  graph:
    connection_pool_size: 3
    timeout: 20.0
    enable_auto_save: true
    store_type: "networkx"
    enable_transactions: true
    backend: "sqlite"  # Graph storage uses SQLite for simplicity

  sql:
    connection_pool_size: 5
    timeout: 30.0
    enable_wal_mode: true
    enable_transactions: true
    # backend inherited from store config

# =============================================================================
# MEMORY SERVICE INTEGRATION
# =============================================================================

# Memory service configuration - controls how MemoryService integrates with layers
memory_service:
  parallel_enabled: true        # Enable M0/M1/M2 parallel processing
  parallel_strategy: "parallel" # Processing strategy
  enable_fallback: true         # Fallback to traditional processing on errors
  log_processing_mode: true     # Log processing decisions
  timeout_per_layer: 30.0       # Timeout per layer (seconds)
  max_retries: 3               # Maximum retry attempts

# =============================================================================
# PERFORMANCE AND MONITORING
# =============================================================================

performance:
  enable_monitoring: true
  enable_caching: true
  cache_size: 1000
  log_interval: 60.0           # performance log interval (seconds)

  # Error handling
  max_error_history: 100
  error_threshold_for_fallback: 0.5  # 50% error rate triggers fallback

# =============================================================================
# ENVIRONMENT-SPECIFIC SETTINGS
# =============================================================================

development:
  enable_debug_logging: false
  enable_performance_profiling: false
  mock_llm_responses: false

production:
  enable_metrics_collection: true
  metrics_export_interval: 300  # seconds
  enable_health_checks: true
  health_check_interval: 60     # seconds
