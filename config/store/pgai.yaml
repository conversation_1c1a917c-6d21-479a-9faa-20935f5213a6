# @package store
# Unified pgai configuration for MemFuse
# This file consolidates all pgai-related settings to eliminate duplication

# =============================================================================
# CORE PGAI BACKEND CONFIGURATION
# =============================================================================

# Primary backend selection
backend: "pgai"

# =============================================================================
# DATABASE CONNECTION SETTINGS
# =============================================================================

# PostgreSQL connection (inherited from database config but can be overridden)
database:
  host: ${oc.env:POSTGRES_HOST,localhost}
  port: ${oc.env:POSTGRES_PORT,5432}
  database: ${oc.env:POSTGRES_DB,memfuse}
  user: ${oc.env:POSTGRES_USER,postgres}
  password: ${oc.env:POSTGRES_PASSWORD,postgres}
  
  # Connection pool settings (unified across all pgai usage)
  pool_size: 10
  max_overflow: 20
  pool_timeout: 30.0
  pool_recycle: 3600

# =============================================================================
# PGAI-SPECIFIC SETTINGS
# =============================================================================

pgai:
  # Core pgai features
  enabled: true
  vectorizer_worker_enabled: true
  auto_embedding: true
  auto_initialize: true

  # Event-driven configuration (enable real-time trigger mode)
  immediate_trigger: true
  
  # Embedding configuration (unified across all layers)
  embedding:
    model: "all-MiniLM-L6-v2"
    dimensions: 384
    batch_size: 100
    
  # Chunking configuration
  chunking:
    chunk_size: 1000
    chunk_overlap: 200
    
  # Performance settings
  performance:
    max_retries: 3
    retry_delay: 5.0
    timeout: 30.0
    enable_transactions: true

  # Event-driven specific settings
  event_driven:
    worker_count: 2              # Number of concurrent processing workers
    queue_size: 100              # Event queue size
    enable_metrics: true         # Enable performance metrics
    notification_timeout: 10.0   # Notification timeout in seconds

# =============================================================================
# TABLE AND VIEW NAMING CONVENTIONS
# =============================================================================

# Unified table naming for different memory layers
tables:
  # M0 layer tables (Raw Data)
  m0:
    messages: "m0_raw"
    metadata: "m0_metadata"
    embedding_view: "m0_raw_embedding"
    vectorizer_name: "m0_raw_vectorizer"

  # M1 layer tables (Episodic Memory)
  m1:
    episodes: "m1_episodic"
    lineage: "m1_lineage"
    metadata: "m1_metadata"
    embedding_view: "m1_episodic_embedding"
    vectorizer_name: "m1_episodic_vectorizer"

  # M2 layer tables (Semantic Memory)
  m2:
    facts: "m2_semantic"
    lineage: "m2_lineage"
    conflicts: "m2_conflicts"
    embedding_view: "m2_semantic_embedding"
    vectorizer_name: "m2_semantic_vectorizer"

  # M3 layer tables (Procedural Memory)
  m3:
    patterns: "m3_procedural"
    skills: "m3_skills"
    behaviors: "m3_behaviors"
    embedding_view: "m3_procedural_embedding"
    vectorizer_name: "m3_procedural_vectorizer"

  # MSMG layer tables (Multi-Scale Mental Graph)
  msmg:
    instances: "msmg_instances"
    ontology: "msmg_ontology"
    relations: "msmg_relations"
    metacognitive: "msmg_metacognitive"
    embedding_view: "msmg_instances_embedding"
    vectorizer_name: "msmg_instances_vectorizer"
    


# =============================================================================
# STORE-SPECIFIC SETTINGS
# =============================================================================

# Store interface settings
store:
  buffer_size: 10
  cache_size: 100
  
  # Query defaults
  top_k: 5
  similarity_threshold: 0.3
  
  # Multi-path retrieval configuration
  multi_path:
    keyword_weight: 0.2
    vector_weight: 0.5
    graph_weight: 0.3
    use_keyword: true
    use_vector: true
    use_graph: false
    fusion_strategy: "rrf"

# =============================================================================
# MEMORY LAYER INTEGRATION (Enhanced for Dual-Layer PgAI)
# =============================================================================

# Memory layer specific configurations with PgAI integration
memory_layers:
  # M0 layer configuration (Raw Data)
  m0:
    enabled: true
    priority: 1
    table_name: "m0_raw"
    
    # PgAI-specific settings for M0
    pgai:
      auto_embedding: true
      immediate_trigger: true
      
    # Processing pipeline configuration
    processing_pipeline: ["storage"]  # M0 only does storage
    
    # Performance settings
    performance:
      max_retries: 3
      retry_interval: 5.0
      worker_count: 2
      queue_size: 100
      batch_size: 10
      
    # Storage backend preferences
    storage_backends: ["vector", "keyword", "sql"]
    
  # M1 layer configuration (Episodic Memory)
  m1:
    enabled: true
    priority: 2
    table_name: "m1_episodic"
    
    # PgAI-specific settings for M1
    pgai:
      auto_embedding: true
      immediate_trigger: true
      
    # Processing pipeline configuration
    processing_pipeline: ["episode_formation", "storage"]  # M1 forms episodes then storage

    # Episode formation configuration
    episode_formation:
      enabled: true
      llm_model: "grok-3-mini"
      temperature: 0.3
      max_tokens: 1000
      context_window: 2  # chunks before/after for context
      temporal_grouping: true
      event_detection: true
      min_episode_length: 1  # minimum chunks per episode
      
    # Performance settings
    performance:
      max_retries: 3
      retry_interval: 5.0
      worker_count: 2
      queue_size: 100
      batch_size: 5  # Smaller batch for LLM processing
      
    # Storage backend preferences
    storage_backends: ["vector", "sql"]  # M1 primarily uses vector and SQL

    # Async processing settings (to avoid blocking M0)
    async_processing:
      enabled: true
      queue_timeout: 30.0
      max_concurrent_extractions: 3
      fallback_on_failure: true  # Continue M0 processing if M1 fails

  # M2 layer configuration (Semantic Memory)
  m2:
    enabled: true
    priority: 3
    table_name: "m2_semantic"

    # PgAI-specific settings for M2
    pgai:
      auto_embedding: true
      immediate_trigger: true

    # Processing pipeline configuration
    processing_pipeline: ["fact_extraction", "storage"]  # M2 extracts facts then storage

    # Fact extraction configuration (M2's primary function)
    fact_extraction:
      enabled: true
      llm_model: "grok-3-mini"
      temperature: 0.3
      max_tokens: 1000
      max_facts_per_chunk: 10
      min_confidence_threshold: 0.7
      batch_size: 5
      context_window: 2  # chunks before/after for context

      # Flexible classification system
      classification_strategy: "open"  # "open", "predefined", "custom"
      enable_auto_categorization: true
      custom_fact_types: []  # Can be extended as needed

      # Conflict resolution
      enable_conflict_detection: true
      conflict_resolution_strategy: "latest"  # "latest", "confidence", "manual"

    # Performance settings
    performance:
      max_retries: 3
      retry_interval: 5.0
      worker_count: 2
      queue_size: 50
      batch_size: 3

    # Storage backend preferences
    storage_backends: ["vector", "sql"]

    # Async processing settings (to avoid blocking M1)
    async_processing:
      enabled: true
      queue_timeout: 30.0
      max_concurrent_extractions: 2
      fallback_on_failure: true

  # M3 layer configuration (Procedural Memory)
  m3:
    enabled: true  # Now fully implemented
    priority: 4

    # M3 table configuration
    tables:
      patterns: "m3_procedural"
      skills: "m3_skills"
      behaviors: "m3_behaviors"

    # PgAI-specific settings for M3
    pgai:
      auto_embedding: true
      immediate_trigger: true
      embedding_model: "all-MiniLM-L6-v2"
      embedding_dimensions: 384

    # Processing pipeline configuration
    processing_pipeline: ["pattern_recognition", "skill_learning", "behavior_adaptation", "storage"]

    # Pattern recognition configuration (M3's primary function)
    pattern_recognition:
      enabled: true
      llm_model: "grok-3-mini"
      temperature: 0.2
      max_tokens: 800
      min_confidence_threshold: 0.8
      batch_size: 2
      context_window: 3  # larger context for pattern detection

    # Skill learning configuration
    skill_learning:
      enabled: true
      proficiency_tracking: true
      decay_modeling: true
      learning_rate_adaptation: true

    # Behavior adaptation configuration
    behavior_adaptation:
      enabled: true
      reinforcement_learning: true
      priority_adjustment: true
      activation_threshold_tuning: true

    # Performance settings
    performance:
      max_retries: 3
      retry_interval: 5.0
      worker_count: 1
      queue_size: 25
      batch_size: 2

    # Storage backend preferences
    storage_backends: ["vector", "sql"]

  # MSMG layer configuration (Multi-Scale Mental Graph)
  msmg:
    enabled: true
    priority: 5

    # MSMG table configuration
    tables:
      instances: "msmg_instances"
      ontology: "msmg_ontology"
      relations: "msmg_relations"
      metacognitive: "msmg_metacognitive"

    # PgAI-specific settings for MSMG
    pgai:
      auto_embedding: true
      immediate_trigger: true
      embedding_model: "all-MiniLM-L6-v2"
      embedding_dimensions: 384

    # Graph construction configuration
    graph_construction:
      enabled: true
      entity_extraction: true
      relation_detection: true
      ontology_mapping: true
      context_grouping: true

    # Meta-cognitive configuration
    metacognitive:
      enabled: true
      provenance_tracking: true
      certainty_modeling: true
      forgetting_mechanism: true
      importance_weighting: true

    # Performance settings
    performance:
      max_retries: 3
      retry_interval: 5.0
      worker_count: 1
      queue_size: 100
      batch_size: 5

    # Storage backend preferences
    storage_backends: ["vector", "graph", "sql"]


# =============================================================================
# STORAGE BACKEND CONFIGURATION
# =============================================================================

# Unified storage backend settings for pgai
storage_backends:
  vector:
    connection_pool_size: 5
    timeout: 30.0
    batch_size: 100
    store_type: "pgai"
    enable_transactions: true
    backend: "pgai"
    
  keyword:
    connection_pool_size: 3
    timeout: 15.0
    enable_transactions: true
    backend: "sqlite"  # Keyword search still uses SQLite for performance
    
  graph:
    connection_pool_size: 3
    timeout: 20.0
    enable_auto_save: true
    store_type: "networkx"
    enable_transactions: true
    backend: "sqlite"  # Graph storage uses SQLite for simplicity
    
  sql:
    connection_pool_size: 5
    timeout: 30.0
    enable_wal_mode: true
    enable_transactions: true
    backend: "pgai"  # SQL operations use pgai/PostgreSQL

# =============================================================================
# ENVIRONMENT-SPECIFIC OVERRIDES
# =============================================================================

# Development environment settings
development:
  pgai:
    vectorizer_worker_enabled: false  # Disable worker in dev for simplicity
    auto_embedding: false             # Manual embedding control in dev
  database:
    pool_size: 5                      # Smaller pool for dev
  performance:
    enable_debug_logging: true
    
# Production environment settings  
production:
  pgai:
    vectorizer_worker_enabled: true   # Enable worker in production
    auto_embedding: true              # Auto embedding in production
  database:
    pool_size: 20                     # Larger pool for production
  performance:
    enable_metrics_collection: true
    metrics_export_interval: 300

# =============================================================================
# MIGRATION AND COMPATIBILITY
# =============================================================================

# Settings for backward compatibility and migration
compatibility:
  legacy_table_names: false          # Use new unified naming convention
  auto_migrate_tables: true          # Automatically migrate old table structures
  preserve_old_data: true            # Keep old data during migration
