# Git
.git
.gitignore
.gitattributes

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Development tools
.pre-commit-config.yaml
noxfile.py
.flake8
.darglint
codecov.yml

# IDE
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
outputs/
logs/
.pytest_cache/
docs/_build/

# Documentation
docs/
*.md
!README.md

# CI/CD
.github/

# Local data (exclude from container, will be mounted)
data/
!data/.gitkeep 