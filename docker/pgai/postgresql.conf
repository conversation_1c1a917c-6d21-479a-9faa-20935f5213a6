# PostgreSQL Configuration for MemFuse PgAI
# Optimized for vector operations and immediate trigger system

# Connection Settings
listen_addresses = '*'
port = 5432
max_connections = 200
superuser_reserved_connections = 3

# Memory Settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 16MB
maintenance_work_mem = 128MB

# WAL Settings
wal_level = replica
max_wal_size = 1GB
min_wal_size = 80MB
checkpoint_completion_target = 0.9

# Query Planner
random_page_cost = 1.1
effective_io_concurrency = 200

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_messages = info
log_min_error_statement = error
log_min_duration_statement = 1000
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_statement = 'ddl'

# Extensions
shared_preload_libraries = 'pgai'

# Vector Extension Settings (for pgvector)
# These will be applied when pgvector is loaded
# max_parallel_workers_per_gather = 2
# max_parallel_workers = 8

# PgAI Specific Settings
# Python path for pgai extension
# pgai.python_path = '/opt/pgai-venv/bin/python'

# Performance Tuning for Vector Operations
# Increase work_mem for vector operations
# work_mem = 32MB for vector queries

# HNSW Index Settings (for pgvector)
# These affect vector index performance
# hnsw.ef_search = 40
# hnsw.m = 16

# Immediate Trigger Optimization
# Settings to optimize NOTIFY/LISTEN performance
tcp_keepalives_idle = 600
tcp_keepalives_interval = 30
tcp_keepalives_count = 3

# Background Writer
bgwriter_delay = 200ms
bgwriter_lru_maxpages = 100
bgwriter_lru_multiplier = 2.0

# Autovacuum (important for vector tables)
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.2
autovacuum_analyze_scale_factor = 0.1

# Statistics
track_activities = on
track_counts = on
track_io_timing = on
track_functions = pl

# Locale
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'

# Default text search configuration
default_text_search_config = 'pg_catalog.english'

# Timezone
timezone = 'UTC'

# SSL (disabled for development)
ssl = off
