[tool.poetry]
name = "memfuse_core"
version = "0.2.1"
description = "MemFuse: lightning-fast, open-source memory layer for LLMs"
authors = [
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
]
license = "Apache-2.0"
readme = "README.md"
homepage = "https://github.com/memfuse/memfuse"
repository = "https://github.com/memfuse/memfuse"
documentation = "https://memfuse.vercel.app"
classifiers = ["Development Status :: 3 - Alpha"]
packages = [{ include = "memfuse_core", from = "src" }]

[tool.poetry.urls]
Changelog = "https://github.com/memfuse/memfuse/releases"

[tool.poetry.dependencies]
aiofiles = "^23.2.1"
aiohttp = "^3.9.3"
asyncpg = "^0.29.0"
einops = "^0.7.0"
fastapi = "^0.110.0"
filetype = "^1.2.0"
hydra-core = "^1.3.2"
ijson = "^3.4.0"
loguru = "^0.7.2"
numpy = "^1.26.4"
omegaconf = "^2.3.0"
openai = "^1.13.3"
pgai = "^0.11.4"
pgvector = "^0.2.0"
psycopg = "^3.1.0"
psycopg-pool = "^3.2.6"
psycopg2-binary = "^2.9.10"
pydantic = "^2.6.3"
python = ">=3.10,<3.13"
python-dotenv = "^1.0.1"
python-igraph = "^0.11.3"
qdrant-client = "^1.8.0"
sentence-transformers = "^2.6.0"
sqlalchemy = "^2.0.41"
tiktoken = "^0.7.0"
transformers = "^4.40.1"
uvicorn = "^0.27.1"

[tool.poetry.group.dev.dependencies]
Pygments = ">=2.19.0"
colorlog = "^6.9.0"
coverage = { extras = ["toml"], version = ">=7.6" }
darglint = ">=1.8.1"
datasets = "^2.18.0"
flake8 = ">=7.0.0"
flake8-bandit = ">=4.1.1"
flake8-bugbear = ">=24.2.6"
flake8-docstrings = ">=1.7.0"
icecream = "^2.1.4"
ipykernel = "^6.29.5"
isort = ">=6.0.0"
mypy = ">=1.12.0"
myst-parser = { version = ">=4.0.0" }
pandas = "^2.2.1"
pep8-naming = ">=0.14.0"
pre-commit = ">=4.0.0"
pre-commit-hooks = ">=5.0.0"
pytest = ">=8.3.4"
pytest-asyncio = "^0.23.5"
pytest-cov = "^4.0.0"
pytest-xdist = "^3.0.0"
pytest-html = "^3.1.0"
pytest-mock = "^3.10.0"
pytest-timeout = "^2.1.0"
jsonschema = "^4.17.3"
pyupgrade = ">=3.19.0"
safety = ">=3.2.0"
tqdm = "^4.66.2"
typeguard = ">=4.4.0"
xdoctest = { extras = ["colors"], version = ">=1.2.0" }

[tool.poetry.scripts]
memfuse-core = "memfuse_core.server:main"

[tool.coverage.paths]
source = ["src/memfuse_core", "*/site-packages/memfuse_core"]
tests = ["tests", "*/tests"]

[tool.coverage.run]
branch = true
source = ["memfuse_core", "tests"]

[tool.coverage.report]
show_missing = true
fail_under = 100

[tool.isort]
profile = "black"
combine_as_imports = true
ensure_newline_before_comments = true
force_grid_wrap = 0
force_single_line = false
include_trailing_comma = true
line_length = 88
lines_after_imports = 2
multi_line_output = 3
use_parentheses = true

[tool.mypy]
strict = true
warn_unreachable = true
pretty = true
show_column_numbers = true
show_error_codes = true
show_error_context = true

[tool.pytest.ini_options]
asyncio_mode = "strict"
asyncio_default_fixture_loop_scope = "function"

[build-system]
requires = ["poetry-core>=2.0.0"]
build-backend = "poetry.core.masonry.api"
