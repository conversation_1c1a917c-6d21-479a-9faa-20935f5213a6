
!benchmarkss/**/*.py
!build/
!data/README.md
!data/README.md
!scripts/**/*.py
!tests/data/README.md
!yarn.lock
*$py.class
**/.mypy_cache/**
**/.venv*/
**/.vscode test/**
**/.vscode-smoke/**
**/.vscode-test/**
**/.vscode/.ropeproject/**
**/node_modules
**/qdrant/meta.json
**/testFiles/**/.cache/**
*.cover
*.db
*.egg
*.egg-info/
*.graphml
*.lock
*.log
*.manifest
*.mo
*.noseids
*.npy
*.pem
*.pkl
*.pot
*.py,cover
*.py[cod]
*.pyc
*.sage.py
*.so
*.spec
*.sqlite
*.tsbuildinfo
*.vsix
*.xlf
.DS_Store
.Python
.cache
.cookiecutter.json
.coverage
.coverage.*
.dmypy.json
.eggs/
.env
.env*.local
.huskyrc.json
.hypothesis/
.idea
.installed.cfg
.ipynb_checkpoints
.mypy_cache/
.nox/
.nyc_output
.pdm.toml
.pnp.js
.pybuilder/
.pyre/
.pytest_cache
.python-version
.pytype/
.ropeproject
.scrapy
.spyderproject
.spyproject
.tox/
.venv
.vercel
.vs/
.vscode
.vscode-test
.webassets-cache
.yarn/install-state.gz
/.coverage.*
/.next/
/.nox/
/.pnp
/.python-version
/.pytype/
/dist/
/docs/_build/
/site
/src/*.egg-info/
ENV/
MANIFEST
__pycache__
__pypackages__/
benchmarks/**/*
bin/**
build/
celerybeat-schedule
celerybeat.pid
cover/
coverage.xml
coverage/
cucumber-report.json
cython_debug/
data.bak/
data/*
data/*
db.sqlite3
db.sqlite3-journal
debug*.log
debug_coverage*/**
debugpy*.log
develop-eggs/
dist/
dmypy.json
docs/_build/
downloads/
eggs/
env.bak/
env/
envVars.txt
htmlcov/
instance/
ipython_config.py
l10n/
languageServer.*/**
languageServer/**
lib64/
local_settings.py
log.log
next-env.d.ts
nodeLanguageServer.*/**
nosetests.xml
npm-debug.log*
obj/**
out
outputs/
package.nls.*.json
parts/
pip-delete-this-directory.txt
pip-log.txt
poetry.lock
port.txt
precommit.hook
profile_default/
pydevd*.log
python_files/get-pip.py
python_files/lib/**
sdist/
share/python-wheels/
target/
test-results*.xml
tests/**/*.json
tests/data/**
tmp/**
var/
venv.bak/
venv/
wheels/
xunit-test-results.xml
yarn-debug.log*