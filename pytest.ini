[pytest]
# Pytest configuration for MemFuse

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --color=yes
    --durations=10

# Markers
markers =
    smoke: Smoke tests for basic functionality
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    e2e: End-to-end tests for full system functionality
    slow: Tests that take a long time to run
    chunking: Tests related to chunking functionality
    api: Tests for API endpoints
    buffer: Tests for buffer functionality
    services: Tests for service layer
    rag: Tests for RAG functionality
    asyncio: Asyncio tests

# Minimum version
minversion = 6.0

# Test timeout (handled by pytest-timeout plugin if installed)

# Asyncio configuration (handled by pytest-asyncio plugin)
asyncio_mode = auto

# Coverage options (if using pytest-cov)
# addopts = --cov=src/memfuse_core --cov-report=html --cov-report=term-missing

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Filterwarnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
