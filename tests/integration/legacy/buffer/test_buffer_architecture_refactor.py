"""Integration tests for <PERSON>uffer Architecture Refactor.

This test suite validates the refactored buffer system with proper abstraction layers:

ARCHITECTURE TESTED:
===================
BufferService (最高层抽象 - 组合三种 Buffer 类型)
├── WriteBuffer (写入路径抽象)
│   ├── RoundBuffer (短期缓存)
│   ├── HybridBuffer (中期缓存 + VectorCache)
│   └── FlushManager (持久化管理)
├── QueryBuffer (查询路径抽象)
│   └── 多源查询和缓存
└── SpeculativeBuffer (预测预取抽象 - PLACEHOLDER)
    └── 预测和预取逻辑

KEY VALIDATIONS:
===============
1. Proper abstraction layers and component isolation
2. W<PERSON><PERSON>uffer manages all write operations internally
3. VectorCache used only for immediate querying, not persisted
4. Data flow: Client → BufferService → WriteBuffer → MemoryService → PostgreSQL
5. Memory Layer event-driven processing via PostgreSQL triggers
6. Component access through proper interfaces
"""

import asyncio
import pytest
import uuid
from typing import Dict, Any, List
from loguru import logger

from memfuse_core.services import MemoryService, BufferService
from memfuse_core.services.database_service import DatabaseService
from memfuse_core.interfaces import MessageList, MessageBatchList


class TestBufferArchitectureRefactor:
    """Test suite for buffer architecture refactor validation."""

    async def setup(self):
        """Setup for each test."""
        # Setup
        self.user_name = f"test_user_{uuid.uuid4().hex[:8]}"
        self.agent_name = "test_agent"
        self.session_name = f"test_session_{uuid.uuid4().hex[:8]}"

        # Initialize services
        self.memory_service = MemoryService(
            user=self.user_name,
            agent=self.agent_name,
            session=self.session_name
        )
        await self.memory_service.initialize()

        # Configure BufferService with small limits to ensure transfers are triggered
        config = {
            "buffer": {
                "round_buffer": {"max_tokens": 100, "max_size": 2},  # Small limits
                "hybrid_buffer": {"max_size": 5}
            }
        }

        self.buffer_service = BufferService(
            memory_service=self.memory_service,
            user=self.user_name,
            config=config
        )
        await self.buffer_service.initialize()

    async def teardown(self):
        """Teardown for each test."""
        try:
            await self.buffer_service.shutdown()
        except Exception as e:
            logger.warning(f"Cleanup error: {e}")

    def create_test_messages(self, count: int = 3, content_prefix: str = "Test message") -> MessageList:
        """Create test messages for testing with sufficient content to trigger transfers."""
        messages = []
        for i in range(count):
            # Create longer content to ensure token limits are reached
            content = f"{content_prefix} {i + 1} - " + "content " * (15 + i * 5)  # Longer content
            messages.append({
                "role": "user" if i % 2 == 0 else "assistant",
                "content": content
            })
        return messages

    async def test_buffer_service_composition(self):
        """Test BufferService properly composes three buffer types."""
        logger.info("🧪 Testing BufferService composition")
        
        # Verify BufferService has all three buffer types
        assert hasattr(self.buffer_service, 'write_buffer')
        assert hasattr(self.buffer_service, 'query_buffer')
        assert hasattr(self.buffer_service, 'speculative_buffer')
        
        # Verify component types
        write_buffer = self.buffer_service.get_write_buffer()
        query_buffer = self.buffer_service.get_query_buffer()
        speculative_buffer = self.buffer_service.get_speculative_buffer()
        
        assert write_buffer is not None
        assert query_buffer is not None
        assert speculative_buffer is not None
        
        logger.info("✅ BufferService composition test passed")

    async def test_write_buffer_internal_composition(self):
        """Test WriteBuffer internally manages RoundBuffer, HybridBuffer, FlushManager."""
        logger.info("🧪 Testing WriteBuffer internal composition")
        
        write_buffer = self.buffer_service.get_write_buffer()
        
        # Verify WriteBuffer has internal components
        assert hasattr(write_buffer, 'round_buffer')
        assert hasattr(write_buffer, 'hybrid_buffer')
        assert hasattr(write_buffer, 'flush_manager')
        
        # Verify component access methods
        round_buffer = write_buffer.get_round_buffer()
        hybrid_buffer = write_buffer.get_hybrid_buffer()
        flush_manager = write_buffer.get_flush_manager()
        
        assert round_buffer is not None
        assert hybrid_buffer is not None
        assert flush_manager is not None
        
        logger.info("✅ WriteBuffer internal composition test passed")

    async def test_write_path_data_flow(self):
        """Test complete write path data flow."""
        logger.info("🧪 Testing write path data flow")
        
        # Create test messages with sufficient content to trigger transfer
        messages = self.create_test_messages(3, "Write path test message")
        
        # Add messages through BufferService (highest abstraction)
        result = await self.buffer_service.add(messages, session_id=self.memory_service._session_id)
        assert result["status"] == "success"
        
        # Verify data flows through WriteBuffer
        write_buffer = self.buffer_service.get_write_buffer()
        stats = write_buffer.get_stats()
        assert stats["write_buffer"]["total_writes"] > 0
        
        # Force flush through WriteBuffer
        flush_result = await write_buffer.flush_all()
        assert flush_result["status"] == "success"
        
        # Wait for async operations
        await asyncio.sleep(1.0)
        
        # Verify data reached database through MemoryService
        db = DatabaseService.get_instance()
        sessions = db.get_sessions(user_id=self.memory_service._user_id)
        assert len(sessions) > 0
        
        logger.info("✅ Write path data flow test passed")

    async def test_vector_cache_behavior(self):
        """Test VectorCache is used for immediate queries but not persisted."""
        logger.info("🧪 Testing VectorCache behavior")
        
        # Add messages to populate VectorCache (ensure transfer is triggered)
        messages = self.create_test_messages(3, "VectorCache test message")
        await self.buffer_service.add(messages, session_id=self.memory_service._session_id)
        
        # Access HybridBuffer through WriteBuffer
        write_buffer = self.buffer_service.get_write_buffer()
        hybrid_buffer = write_buffer.get_hybrid_buffer()
        
        # Verify VectorCache has data
        assert len(hybrid_buffer.chunks) > 0
        assert len(hybrid_buffer.embeddings) > 0
        assert len(hybrid_buffer.original_rounds) > 0
        
        # Test immediate query from VectorCache
        query_result = await self.buffer_service.query("test message", top_k=2)
        assert query_result["status"] == "success"
        assert len(query_result["data"]["results"]) > 0
        
        # Trigger flush (should clear VectorCache but persist rounds)
        await hybrid_buffer.flush_to_storage()
        
        # Verify VectorCache is cleared after flush
        assert len(hybrid_buffer.chunks) == 0
        assert len(hybrid_buffer.embeddings) == 0
        assert len(hybrid_buffer.original_rounds) == 0
        
        logger.info("✅ VectorCache behavior test passed")

    async def test_component_access_abstraction(self):
        """Test proper component access through abstraction layers."""
        logger.info("🧪 Testing component access abstraction")
        
        # Test high-level component access
        write_buffer = self.buffer_service.get_write_buffer()
        query_buffer = self.buffer_service.get_query_buffer()
        speculative_buffer = self.buffer_service.get_speculative_buffer()
        
        assert write_buffer is not None
        assert query_buffer is not None
        assert speculative_buffer is not None
        
        # Test legacy component access (backward compatibility)
        round_buffer = self.buffer_service.get_round_buffer()
        hybrid_buffer = self.buffer_service.get_hybrid_buffer()
        flush_manager = self.buffer_service.get_flush_manager()
        
        assert round_buffer is not None
        assert hybrid_buffer is not None
        assert flush_manager is not None
        
        # Verify legacy access goes through WriteBuffer
        assert round_buffer is write_buffer.get_round_buffer()
        assert hybrid_buffer is write_buffer.get_hybrid_buffer()
        assert flush_manager is write_buffer.get_flush_manager()
        
        logger.info("✅ Component access abstraction test passed")

    async def test_speculative_buffer_placeholder(self):
        """Test SpeculativeBuffer placeholder functionality."""
        logger.info("🧪 Testing SpeculativeBuffer placeholder")
        
        speculative_buffer = self.buffer_service.get_speculative_buffer()
        
        # Test placeholder methods
        await speculative_buffer.update([{"content": "test"}])
        prefetched = await speculative_buffer.get_prefetched("test query")
        stats = speculative_buffer.get_stats()
        
        # Verify placeholder behavior
        assert isinstance(prefetched, list)
        assert stats["status"] == "placeholder"
        assert stats["type"] == "SpeculativeBuffer"
        
        logger.info("✅ SpeculativeBuffer placeholder test passed")

    async def test_end_to_end_architecture_flow(self):
        """Test complete end-to-end flow through new architecture."""
        logger.info("🧪 Testing end-to-end architecture flow")
        
        # 1. Add messages through BufferService (highest abstraction)
        messages = self.create_test_messages(4, "End-to-end test message")
        add_result = await self.buffer_service.add(messages, session_id=self.memory_service._session_id)
        assert add_result["status"] == "success"
        
        # 2. Query from VectorCache (immediate)
        query_result = await self.buffer_service.query("test message", top_k=2)
        assert query_result["status"] == "success"
        assert len(query_result["data"]["results"]) > 0
        
        # 3. Force flush through WriteBuffer abstraction
        write_buffer = self.buffer_service.get_write_buffer()
        flush_result = await write_buffer.flush_all()
        assert flush_result["status"] == "success"
        
        # 4. Wait for async processing
        await asyncio.sleep(1.0)
        
        # 5. Verify data in database
        db = DatabaseService.get_instance()
        sessions = db.get_sessions(user_id=self.memory_service._user_id)
        assert len(sessions) > 0, "No sessions found in database"

        # Verify session exists (this confirms data was persisted)
        session_id = sessions[0]["id"]
        assert session_id == self.memory_service._session_id, "Session ID mismatch"
        
        # 6. Query from persistent storage (skip if vector store not available)
        try:
            storage_query_result = await self.memory_service.query("test message", top_k=2)
            assert storage_query_result["status"] == "success"
            logger.info("✅ Storage query successful")
        except Exception as e:
            logger.warning(f"⚠️ Storage query skipped due to missing vector store: {e}")
            # This is acceptable in test environment without full vector store setup
        
        logger.info("✅ End-to-end architecture flow test passed")

    async def test_abstraction_layer_isolation(self):
        """Test that abstraction layers are properly isolated."""
        logger.info("🧪 Testing abstraction layer isolation")
        
        # BufferService should not directly access low-level components
        assert not hasattr(self.buffer_service, 'round_buffer')
        assert not hasattr(self.buffer_service, 'hybrid_buffer')
        assert not hasattr(self.buffer_service, 'flush_manager')
        
        # BufferService should only have high-level buffer types
        assert hasattr(self.buffer_service, 'write_buffer')
        assert hasattr(self.buffer_service, 'query_buffer')
        assert hasattr(self.buffer_service, 'speculative_buffer')
        
        # WriteBuffer should manage its internal components
        write_buffer = self.buffer_service.get_write_buffer()
        assert hasattr(write_buffer, 'round_buffer')
        assert hasattr(write_buffer, 'hybrid_buffer')
        assert hasattr(write_buffer, 'flush_manager')
        
        logger.info("✅ Abstraction layer isolation test passed")


if __name__ == "__main__":
    """Run tests directly."""
    async def run_tests():
        test_instance = TestBufferArchitectureRefactor()

        tests = [
            ("BufferService Composition", test_instance.test_buffer_service_composition),
            ("WriteBuffer Internal Composition", test_instance.test_write_buffer_internal_composition),
            ("Write Path Data Flow", test_instance.test_write_path_data_flow),
            ("VectorCache Behavior", test_instance.test_vector_cache_behavior),
            ("Component Access Abstraction", test_instance.test_component_access_abstraction),
            ("SpeculativeBuffer Placeholder", test_instance.test_speculative_buffer_placeholder),
            ("End-to-End Architecture Flow", test_instance.test_end_to_end_architecture_flow),
            ("Abstraction Layer Isolation", test_instance.test_abstraction_layer_isolation),
        ]

        passed = 0
        total = len(tests)

        for test_name, test_func in tests:
            logger.info(f"\n📋 Running {test_name} Test...")
            try:
                # Setup for each test
                await test_instance.setup()

                # Run test
                await test_func()

                logger.info(f"✅ {test_name} Test: PASSED")
                passed += 1

            except Exception as e:
                logger.error(f"❌ {test_name} Test: FAILED - {e}")
                import traceback
                traceback.print_exc()

            finally:
                # Teardown for each test
                try:
                    await test_instance.teardown()
                except Exception as e:
                    logger.warning(f"Teardown error for {test_name}: {e}")

        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("📊 Buffer Architecture Refactor Test Results:")
        logger.info(f"🎯 Overall: {passed}/{total} tests passed")

        if passed == total:
            logger.info("🎉 All buffer architecture refactor tests passed!")
            return True
        else:
            logger.error("💥 Some tests failed. Please check the logs above.")
            return False

    # Run the tests
    success = asyncio.run(run_tests())
    exit(0 if success else 1)
