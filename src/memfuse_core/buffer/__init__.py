"""Buffer system for MemFuse.

The buffer system optimizes data flow between the client and storage layers,
improving performance and enabling advanced features:

1. Write<PERSON>uffer (Write Combining Buffer):
   Queues incoming data for batch processing, optimizing write operations
2. SpeculativeBuffer (Speculative Prefetch Buffer):
   Proactively retrieves data that might be needed soon, reducing latency
3. Query<PERSON>uffer (Heterogeneous Query Cache):
   Manages retrieval and ranking of data from multiple sources
4. EvictionBuffer (Cache Replacement Buffer):
   Manages memory usage by selectively removing less important items
5. LocalityBuffer (Temporal and Semantic Locality Buffer):
   Optimizes access to related items
6. CoherencyController (Cache Coherence Controller):
   Ensures data consistency across multiple buffers

The buffer system serves as a foundation for the hierarchical memory system (M0/M1/M2).
"""

# Buffer components
from .round_buffer import RoundBuffer
from .hybrid_buffer import HybridBuffer
from .query_buffer import QueryBuffer

__all__ = [
    "RoundBuffer",
    "HybridBuffer",
    "QueryBuffer",
]
