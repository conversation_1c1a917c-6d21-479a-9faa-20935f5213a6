"""Chunk module for MemFuse RAG system.

This module provides chunking functionality for processing message lists
into retrievable chunks with various strategies.
"""

from .base import ChunkData, ChunkStrategy
from .message import MessageChunkStrategy
from .character import CharacterChunkStrategy
from .contextual import ContextualChunkStrategy

__all__ = [
    "ChunkData",
    "ChunkStrategy",
    "MessageChunkStrategy",
    "ContextualChunkStrategy",
    "CharacterChunkStrategy",
]
